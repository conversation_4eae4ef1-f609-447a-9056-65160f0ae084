import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { Link } from "@/i18n/navigation";
import { Section as SectionType } from "@/types/blocks/section";

export default function CTA({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="px-8">
        <div className='flex items-center justify-center rounded-2xl  bg-[url("/imgs/masks/circle.svg")] bg-cover bg-center px-8 py-12 text-center md:p-16'>
          <div className="mx-auto max-w-(--breakpoint-md)">
            <h2 className="mb-4 text-balance text-3xl font-semibold md:text-5xl">
              {section.title}
            </h2>
            <p
              className="m mx-auto max-w-3xl text-muted-foreground lg:text-xl"
              dangerouslySetInnerHTML={{ __html: section.description || "" }}
            />
            {/* TODO */}
            {section.buttons && (
              <div className="mt-8 flex flex-col justify-center gap-6 sm:flex-row">
                {section.buttons.map((item, idx) => {
                  const isPrimary = item.variant === "default" || !item.variant;
                  return (
                    <Link
                      key={idx}
                      href={(item.url as any) || ""}
                      target={item.target || ""}
                      className="group flex items-center justify-center"
                    >
                      <Button
                        className={`
                          relative overflow-hidden px-8 py-6 text-lg font-semibold
                          transition-all duration-300 ease-out rounded-full
                          ${
                            isPrimary
                              ? "bg-gradient-to-r from-red-500 via-orange-500 to-red-600 animate-gradient-x hover:animate-none hover:from-red-400 hover:via-orange-400 hover:to-red-500 shadow-lg shadow-red-500/30 hover:shadow-xl hover:shadow-red-500/50 text-white border-0"
                              : "border-2 border-red-300/30 hover:border-red-400/50 bg-background/50 hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50"
                          }
                          hover:scale-105 hover:-translate-y-1
                          active:scale-95 active:translate-y-0
                          min-w-[200px] sm:min-w-[220px]
                        `}
                        size="lg"
                        variant={item.variant || "default"}
                      >
                        {isPrimary && (
                          <>
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
                            <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 via-orange-400/20 to-red-400/20 animate-pulse" />
                          </>
                        )}

                        <div className="relative flex items-center gap-3 z-10">
                          {item.icon && (
                            <Icon
                              name={item.icon}
                              className="size-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"
                            />
                          )}
                          <span className="relative z-10 font-bold tracking-wide">
                            {item.title}
                          </span>
                        </div>

                        {isPrimary && (
                          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-red-500/30 via-orange-500/30 to-red-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
                        )}
                      </Button>
                    </Link>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
